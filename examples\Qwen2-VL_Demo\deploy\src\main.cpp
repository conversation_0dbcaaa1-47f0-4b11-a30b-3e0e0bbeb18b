// Copyright (c) 2024 by Rockchip Electronics Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <iostream>
#include <fstream>
#include <chrono>
#include <sstream>
#include <vector>
#include <opencv2/opencv.hpp>

#include "image_enc.h"
#include "rkllm.h"

#define IMAGE_HEIGHT 392
#define IMAGE_WIDTH 392
#define IMAGE_TOKEN_NUM 196
#define EMBED_SIZE 1536

using namespace std;
LLMHandle llmHandle = nullptr;

void exit_handler(int signal)
{
    if (llmHandle != nullptr)
    {
        {
            cout << "程序即将退出" << endl;
            LLMHandle _tmp = llmHandle;
            llmHandle = nullptr;
            rkllm_destroy(_tmp);
        }
    }
    exit(signal);
}

int callback(RKLLMResult *result, void *userdata, LLMCallState state)
{

    if (state == RKLLM_RUN_FINISH)
    {
        printf("\n");
    }
    else if (state == RKLLM_RUN_ERROR)
    {
        printf("\\run error\n");
    }
    else if (state == RKLLM_RUN_NORMAL)
    {
        printf("%s", result->text);
        // for(int i=0; i<result->num; i++)
        // {
        //     printf("%d token_id: %d logprob: %f\n", i, result->tokens[i].id, result->tokens[i].logprob);
        // }
    }
    return 0;
}

// Expand the image into a square and fill it with the specified background color
cv::Mat expand2square(const cv::Mat& img, const cv::Scalar& background_color) {
    int width = img.cols;
    int height = img.rows;

    // If the width and height are equal, return to the original image directly
    if (width == height) {
        return img.clone();
    }

    // Calculate the new size and create a new image
    int size = std::max(width, height);
    cv::Mat result(size, size, img.type(), background_color);

    // Calculate the image paste position
    int x_offset = (size - width) / 2;
    int y_offset = (size - height) / 2;

    // Paste the original image into the center of the new image
    cv::Rect roi(x_offset, y_offset, width, height);
    img.copyTo(result(roi));

    return result;
}

int main(int argc, char** argv)
{
    if (argc < 5) {
        std::cerr << "Usage: " << argv[0] << " encoder_model_path llm_model_path max_new_tokens max_context_len rknn_core_num\n";
        std::cerr << "Interactive mode: After loading models, input format: <image_path> <text_prompt>\n";
        std::cerr << "Example: demo.jpg What is in this image?\n";
        std::cerr << "Exit: Ctrl+C or type 'exit'\n";
        return -1;
    }

    const char * encoder_model_path = argv[1];

    //设置llm参数及初始化
    RKLLMParam param = rkllm_createDefaultParam();
    param.model_path = argv[2];
    param.top_k = 1;
    param.max_new_tokens = std::atoi(argv[3]);
    param.max_context_len = std::atoi(argv[4]);
    param.skip_special_token = true;
    param.img_start = "<|vision_start|>";
    param.img_end = "<|vision_end|>";
    param.img_content = "<|image_pad|>";
    param.extend_param.base_domain_id = 1;
    int ret;

    std::chrono::high_resolution_clock::time_point t_start_us = std::chrono::high_resolution_clock::now();

    ret = rkllm_init(&llmHandle, &param, callback);
    if (ret == 0){
        printf("rkllm init success\n");
    } else {
        printf("rkllm init failed\n");
        exit_handler(-1);
    }
    std::chrono::high_resolution_clock::time_point t_load_end_us = std::chrono::high_resolution_clock::now();

    auto load_time = std::chrono::duration_cast<std::chrono::microseconds>(t_load_end_us - t_start_us);
    printf("%s: LLM Model loaded in %8.2f ms\n", __func__, load_time.count() / 1000.0);

    // imgenc初始化
    rknn_app_context_t rknn_app_ctx;
    memset(&rknn_app_ctx, 0, sizeof(rknn_app_context_t));

    t_start_us = std::chrono::high_resolution_clock::now();

    const int core_num = atoi(argv[5]);
    ret = init_imgenc(encoder_model_path, &rknn_app_ctx, core_num);
    if (ret != 0) {
        printf("init_imgenc fail! ret=%d model_path=%s\n", ret, encoder_model_path);
        return -1;
    }
    t_load_end_us = std::chrono::high_resolution_clock::now();

    load_time = std::chrono::duration_cast<std::chrono::microseconds>(t_load_end_us - t_start_us);
    printf("%s: ImgEnc Model loaded in %8.2f ms\n", __func__, load_time.count() / 1000.0);

    // 图片处理将在交互循环中进行
    
    RKLLMInput rkllm_input;
    memset(&rkllm_input, 0, sizeof(RKLLMInput));  // 重要：初始化结构体，避免未初始化数据影响推理

    // 初始化 infer 参数结构体
    RKLLMInferParam rkllm_infer_params;
    memset(&rkllm_infer_params, 0, sizeof(RKLLMInferParam));
    rkllm_infer_params.mode = RKLLM_INFER_GENERATE;

    rkllm_infer_params.keep_history = 0;
    rkllm_set_chat_template(llmHandle, "<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "<|im_end|>\n<|im_start|>assistant\n");

    // 交互模式
    cout << "\n=== Interactive Multimodal Chat ===" << endl;
    cout << "Models loaded successfully!" << endl;
    cout << "Input format: <image_path> <text_prompt>" << endl;
    cout << "Example: demo.jpg What is in this image?" << endl;
    cout << "Exit: Ctrl+C or type 'exit'" << endl;
    cout << "===================================\n" << endl;

    while(true) {
        std::string input_line;
        printf("\nuser: ");
        std::getline(std::cin, input_line);

        // 检查退出命令
        if (input_line == "exit") {
            break;
        }

        // 解析输入：图片路径和文本提示
        std::istringstream iss(input_line);
        std::string image_path;
        std::string text_prompt;

        // 获取第一个参数（图片路径）
        if (!(iss >> image_path)) {
            printf("Error: Please provide image path and text prompt\n");
            printf("Format: <image_path> <text_prompt>\n");
            continue;
        }

        // 获取剩余部分作为文本提示
        std::getline(iss, text_prompt);
        if (text_prompt.empty()) {
            printf("Error: Please provide text prompt after image path\n");
            printf("Format: <image_path> <text_prompt>\n");
            continue;
        }

        // 移除文本提示前面的空格
        text_prompt.erase(0, text_prompt.find_first_not_of(" \t"));

        printf("Processing image: %s\n", image_path.c_str());
        printf("Text prompt: %s\n", text_prompt.c_str());

        // 加载和处理图片
        cv::Mat img = cv::imread(image_path);
        if (img.empty()) {
            printf("Error: Cannot load image '%s'\n", image_path.c_str());
            continue;
        }

        cv::cvtColor(img, img, cv::COLOR_BGR2RGB);

        // Expand the image into a square and fill it with the specified background color
        cv::Scalar background_color(127.5, 127.5, 127.5);
        cv::Mat square_img = expand2square(img, background_color);

        // Resize the image to 392x392
        cv::Mat resized_img;
        cv::Size new_size(IMAGE_WIDTH, IMAGE_HEIGHT);
        cv::resize(square_img, resized_img, new_size, 0, 0, cv::INTER_LINEAR);

        size_t n_image_tokens = IMAGE_TOKEN_NUM;
        size_t image_embed_len = EMBED_SIZE;
        int rkllm_image_embed_len = n_image_tokens * image_embed_len;
        float img_vec[rkllm_image_embed_len];
        ret = run_imgenc(&rknn_app_ctx, resized_img.data, img_vec);
        if (ret != 0) {
            printf("Error: Image encoding failed! ret=%d\n", ret);
            continue;
        }

        // 重新初始化输入结构体，确保没有残留数据
        memset(&rkllm_input, 0, sizeof(RKLLMInput));

        // 设置多模态输入
        rkllm_input.input_type = RKLLM_INPUT_MULTIMODAL;
        rkllm_input.role = "user";
        rkllm_input.multimodal_input.prompt = (char*)text_prompt.c_str();
        rkllm_input.multimodal_input.image_embed = img_vec;
        rkllm_input.multimodal_input.n_image_tokens = n_image_tokens;
        rkllm_input.multimodal_input.n_image = 1;
        rkllm_input.multimodal_input.image_height = IMAGE_HEIGHT;
        rkllm_input.multimodal_input.image_width = IMAGE_WIDTH;

        printf("robot: ");
        rkllm_run(llmHandle, &rkllm_input, &rkllm_infer_params, NULL);
        printf("\n");
    }

    ret = release_imgenc(&rknn_app_ctx);
    if (ret != 0) {
        printf("release_imgenc fail! ret=%d\n", ret);
    }
    rkllm_destroy(llmHandle);

    return 0;
}